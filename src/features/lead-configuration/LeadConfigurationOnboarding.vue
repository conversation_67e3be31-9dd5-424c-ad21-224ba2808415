<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useToast } from 'primevue/usetoast';
import { LeadConfigService } from './services/leadConfigService';
import ConfigurationManager from './components/ConfigurationManager.vue';
import type { 
    LeadConfiguration, 
    ConfigurationType, 
    CONFIGURATION_SECTIONS 
} from './types';
import { CONFIGURATION_SECTIONS } from './types';

// Props
interface Props {
    visible?: boolean;
    showHeader?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    visible: true,
    showHeader: true
});

// Emits
const emit = defineEmits<{
    complete: [];
    close: [];
}>();

// Reactive state
const loading = ref(false);
const configuration = ref<LeadConfiguration | null>(null);
const activeTab = ref<ConfigurationType>('lead_actions');
const hasUnsavedChanges = ref(false);
const toast = useToast();

// Computed
const currentSection = computed(() => {
    return CONFIGURATION_SECTIONS.find(section => section.key === activeTab.value);
});

const currentConfig = computed(() => {
    if (!configuration.value || !activeTab.value) return {};
    return configuration.value[activeTab.value] || {};
});

// Methods
const loadConfiguration = async () => {
    loading.value = true;
    try {
        const config = await LeadConfigService.getTenantConfiguration();
        if (config) {
            configuration.value = config;
        } else {
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to load configuration',
                life: 3000
            });
        }
    } catch (error) {
        console.error('Error loading configuration:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load configuration',
            life: 3000
        });
    } finally {
        loading.value = false;
    }
};

const handleConfigurationUpdate = async (
    configurationType: ConfigurationType,
    updatedConfig: any
) => {
    try {
        const success = await LeadConfigService.updateTenantConfiguration(
            configurationType,
            updatedConfig
        );

        if (success) {
            // Update local configuration
            if (configuration.value) {
                configuration.value[configurationType] = updatedConfig;
            }
            
            hasUnsavedChanges.value = false;
            toast.add({
                severity: 'success',
                summary: 'Success',
                detail: `${currentSection.value?.title} updated successfully`,
                life: 3000
            });
        } else {
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to update configuration',
                life: 3000
            });
        }
    } catch (error) {
        console.error('Error updating configuration:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update configuration',
            life: 3000
        });
    }
};

const handleTabChange = (newTab: ConfigurationType) => {
    if (hasUnsavedChanges.value) {
        // Could add confirmation dialog here
        console.warn('Switching tabs with unsaved changes');
    }
    activeTab.value = newTab;
    hasUnsavedChanges.value = false;
};

const handleComplete = () => {
    if (hasUnsavedChanges.value) {
        toast.add({
            severity: 'warn',
            summary: 'Warning',
            detail: 'Please save your changes before completing',
            life: 3000
        });
        return;
    }
    
    emit('complete');
};

const handleClose = () => {
    if (hasUnsavedChanges.value) {
        // Could add confirmation dialog here
        console.warn('Closing with unsaved changes');
    }
    emit('close');
};

const resetToDefaults = async () => {
    if (!activeTab.value) return;
    
    try {
        const success = await LeadConfigService.resetToDefaults(activeTab.value);
        if (success) {
            await loadConfiguration();
            toast.add({
                severity: 'success',
                summary: 'Success',
                detail: `${currentSection.value?.title} reset to defaults`,
                life: 3000
            });
        } else {
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to reset to defaults',
                life: 3000
            });
        }
    } catch (error) {
        console.error('Error resetting to defaults:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to reset to defaults',
            life: 3000
        });
    }
};

// Lifecycle
onMounted(async () => {
    await loadConfiguration();
    
    // Subscribe to real-time updates
    LeadConfigService.subscribeToTenantChanges((config) => {
        if (config && !hasUnsavedChanges.value) {
            configuration.value = config;
        }
    });
});

onUnmounted(() => {
    LeadConfigService.unsubscribeFromTenantChanges();
});
</script>

<template>
    <div class="lead-configuration">
        <!-- Header -->
        <div v-if="showHeader" class="configuration-header mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="text-2xl font-bold text-surface-900 dark:text-surface-0 mb-2">
                        Lead Configuration
                    </h2>
                    <p class="text-surface-600 dark:text-surface-400">
                        Customize your lead actions, sources, and statuses to match your business workflow
                    </p>
                </div>
                <div class="flex gap-2">
                    <Button 
                        label="Reset to Defaults" 
                        icon="pi pi-refresh" 
                        severity="secondary" 
                        outlined
                        @click="resetToDefaults"
                        :disabled="loading"
                    />
                    <Button 
                        label="Complete" 
                        icon="pi pi-check" 
                        @click="handleComplete"
                        :disabled="loading || hasUnsavedChanges"
                    />
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-8">
            <ProgressSpinner />
        </div>

        <!-- Configuration Content -->
        <div v-else-if="configuration" class="configuration-content">
            <!-- Tab Navigation -->
            <div class="configuration-tabs mb-6">
                <div class="flex border-b border-surface-200 dark:border-surface-700">
                    <button
                        v-for="section in CONFIGURATION_SECTIONS"
                        :key="section.key"
                        @click="handleTabChange(section.key)"
                        :class="[
                            'flex items-center gap-2 px-4 py-3 font-medium text-sm transition-colors',
                            'border-b-2 border-transparent hover:text-primary-600 hover:border-primary-300',
                            activeTab === section.key
                                ? 'text-primary-600 border-primary-600 bg-primary-50 dark:bg-primary-900/20'
                                : 'text-surface-600 dark:text-surface-400'
                        ]"
                    >
                        <i :class="section.icon"></i>
                        {{ section.title }}
                    </button>
                </div>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
                <div v-if="currentSection" class="section-info mb-6">
                    <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0 mb-2">
                        {{ currentSection.title }}
                    </h3>
                    <p class="text-surface-600 dark:text-surface-400">
                        {{ currentSection.description }}
                    </p>
                </div>

                <!-- Configuration Manager -->
                <ConfigurationManager
                    v-if="activeTab && currentConfig"
                    :configuration-type="activeTab"
                    :initial-config="currentConfig"
                    @update="handleConfigurationUpdate"
                    @changes="hasUnsavedChanges = $event"
                />
            </div>
        </div>

        <!-- Error State -->
        <div v-else class="flex flex-col items-center justify-center py-8">
            <i class="pi pi-exclamation-triangle text-4xl text-orange-500 mb-4"></i>
            <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0 mb-2">
                Failed to Load Configuration
            </h3>
            <p class="text-surface-600 dark:text-surface-400 mb-4">
                There was an error loading your lead configuration.
            </p>
            <Button 
                label="Retry" 
                icon="pi pi-refresh" 
                @click="loadConfiguration"
            />
        </div>
    </div>
</template>

<style scoped>
.lead-configuration {
    @apply w-full max-w-6xl mx-auto p-6;
}

.configuration-tabs button {
    @apply relative;
}

.configuration-tabs button:hover {
    @apply bg-surface-50 dark:bg-surface-800;
}

.tab-content {
    @apply min-h-96;
}

.section-info {
    @apply p-4 bg-surface-50 dark:bg-surface-800 rounded-lg border border-surface-200 dark:border-surface-700;
}
</style>
