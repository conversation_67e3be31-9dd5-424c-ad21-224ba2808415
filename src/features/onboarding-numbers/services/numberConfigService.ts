import { ref, computed } from 'vue';
import { useAuthStore } from '@/entities/auth';
import { useTenantStore } from '@/entities/tenant';
import type { Tenant } from '@/entities/tenant/model';
import type { ServiceConfiguration, NumberPurchaseResponse } from '../types';

/**
 * Number Configuration Service
 * Handles auth and tenant store interactions for the onboarding-numbers feature
 */

export interface NumberConfigState {
    isLoading: boolean;
    error: string | null;
    userData: any;
    tenant: Tenant | null;
    tenantId: string | null;
    serviceConfig: ServiceConfiguration;
}

export class NumberConfigService {
    private authStore = useAuthStore();
    private tenantStore = useTenantStore();
    private tenantUnsubscribe: (() => void) | null = null;

    // State
    public state = ref<NumberConfigState>({
        isLoading: false,
        error: null,
        userData: null,
        tenant: null,
        tenantId: null,
        serviceConfig: {
            sms: { enabled: false },
            call: { enabled: false },
            ai_call: { enabled: false },
            useSharedNumber: false,
            useTwilioSharedNumber: false
        }
    });

    // Computed properties
    public isReady = computed(() => !this.state.value.isLoading && this.state.value.userData && this.state.value.tenant);

    public hasError = computed(() => !!this.state.value.error);

    public tenantId = computed(() => this.state.value.tenantId);

    public tenant = computed(() => this.state.value.tenant);

    public serviceConfig = computed(() => this.state.value.serviceConfig);

    /**
     * Initialize the service
     */
    public async initialize(): Promise<void> {
        try {
            this.state.value.isLoading = true;
            this.clearError();

            // Load user data
            await this.loadUserData();

            // Load tenant data if we have a tenant ID
            if (this.state.value.tenantId) {
                await this.loadTenant();
                this.subscribeToTenantChanges();
            }
        } catch (error) {
            this.setError(error instanceof Error ? error.message : 'Initialization failed');
            throw error;
        } finally {
            this.state.value.isLoading = false;
        }
    }

    /**
     * Load user data from auth store
     */
    private async loadUserData(): Promise<void> {
        const userData = await this.authStore.getUserData();
        if (!userData) {
            throw new Error('No user data found. Please log in again.');
        }

        this.state.value.userData = userData;
        this.state.value.tenantId = userData.tenantId;

        if (!userData.tenantId) {
            throw new Error('User is not associated with a tenant.');
        }
    }

    /**
     * Load tenant data
     */
    private async loadTenant(): Promise<void> {
        if (!this.state.value.tenantId) {
            throw new Error('No tenant ID available');
        }

        const tenant = await this.tenantStore.getTenant(this.state.value.tenantId);
        if (!tenant) {
            throw new Error('Tenant not found');
        }

        this.state.value.tenant = tenant;
        this.updateServiceConfigFromTenant(tenant);
    }

    /**
     * Subscribe to tenant changes
     */
    private subscribeToTenantChanges(): void {
        if (!this.state.value.tenantId) return;

        this.unsubscribeFromTenant();

        this.tenantUnsubscribe = this.tenantStore.subscribeToTenantChanges(this.state.value.tenantId, (tenant) => {
            this.state.value.tenant = tenant;
            if (tenant) {
                this.updateServiceConfigFromTenant(tenant);
            }
        });
    }

    /**
     * Update service configuration based on tenant data
     */
    private updateServiceConfigFromTenant(tenant: Tenant): void {
        const config: ServiceConfiguration = {
            sms: {
                enabled: !!tenant.sms?.defaultNumber,
                number: tenant.sms?.defaultNumber || ''
            },
            call: {
                enabled: !!tenant.call?.defaultNumber,
                number: tenant.call?.defaultNumber || ''
            },
            ai_call: {
                enabled: !!tenant.ai_call?.defaultNumber,
                number: tenant.ai_call?.defaultNumber || ''
            },
            useSharedNumber: false,
            useTwilioSharedNumber: false
        };

        // Check if using Twilio shared number (SMS and Call same, but different from AI)
        const smsNumber = tenant.sms?.defaultNumber;
        const callNumber = tenant.call?.defaultNumber;
        const aiCallNumber = tenant.ai_call?.defaultNumber;

        if (smsNumber && callNumber && smsNumber === callNumber && smsNumber !== aiCallNumber) {
            config.useTwilioSharedNumber = true;
            config.twilioSharedNumber = smsNumber;
        }
        // Legacy shared number (only when AI calls not enabled)
        else if (smsNumber && smsNumber === callNumber && !aiCallNumber) {
            config.useSharedNumber = true;
            config.sharedNumber = smsNumber;
        }

        this.state.value.serviceConfig = config;
    }

    /**
     * Update service configuration
     */
    public updateServiceConfig(config: ServiceConfiguration): void {
        this.state.value.serviceConfig = config;
    }

    /**
     * Update tenant with purchased numbers
     */
    public async updateTenantWithNumbers(serviceConfig: ServiceConfiguration, purchaseResults: Record<string, NumberPurchaseResponse>): Promise<void> {
        if (!this.state.value.tenantId) {
            throw new Error('No tenant ID available for update');
        }

        try {
            this.state.value.isLoading = true;
            this.clearError();

            const updateData: any = {};

            // Handle Twilio shared number
            if (serviceConfig.useTwilioSharedNumber || (serviceConfig.useSharedNumber && !serviceConfig.ai_call.enabled)) {
                const sharedResult = purchaseResults.twilio_shared;
                if (sharedResult?.success && sharedResult.phoneNumber) {
                    const sharedNumber = sharedResult.phoneNumber;

                    if (serviceConfig.sms.enabled) {
                        updateData.sms = {
                            defaultNumber: sharedNumber,
                            numbers: [sharedNumber],
                            validated: false
                        };
                    }

                    if (serviceConfig.call.enabled) {
                        updateData.call = {
                            defaultNumber: sharedNumber,
                            numbers: [sharedNumber],
                            validated: false
                        };
                    }
                }
            } else {
                // Individual Twilio numbers
                if (purchaseResults.sms?.success && purchaseResults.sms.phoneNumber) {
                    updateData.sms = {
                        defaultNumber: purchaseResults.sms.phoneNumber,
                        numbers: [purchaseResults.sms.phoneNumber],
                        validated: false
                    };
                }

                if (purchaseResults.call?.success && purchaseResults.call.phoneNumber) {
                    updateData.call = {
                        defaultNumber: purchaseResults.call.phoneNumber,
                        numbers: [purchaseResults.call.phoneNumber],
                        validated: false
                    };
                }
            }

            // Handle AI Call number (always separate)
            if (purchaseResults.ai_call?.success && purchaseResults.ai_call.phoneNumber) {
                updateData.ai_call = {
                    defaultNumber: purchaseResults.ai_call.phoneNumber,
                    numbers: [purchaseResults.ai_call.phoneNumber],
                    validated: false
                };
            }

            await this.tenantStore.updateTenant(this.state.value.tenantId, updateData);
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to update tenant';
            this.setError(errorMessage);
            throw error;
        } finally {
            this.state.value.isLoading = false;
        }
    }

    /**
     * Check if a service is enabled in lead sources
     */
    public isServiceEnabledInLeadSources(service: 'sms' | 'call' | 'ai_call'): boolean {
        if (!this.state.value.tenant?.lead_sources) return true;
        return this.state.value.tenant.lead_sources[service]?.status === 'enabled';
    }

    /**
     * Get validation status for services
     */
    public getValidationStatus(): Record<string, boolean> {
        const tenant = this.state.value.tenant;
        if (!tenant) return {};

        return {
            sms: tenant.sms?.validated || false,
            call: tenant.call?.validated || false,
            ai_call: tenant.ai_call?.validated || false
        };
    }

    /**
     * Refresh tenant data
     */
    public async refresh(): Promise<void> {
        if (this.state.value.tenantId) {
            await this.loadTenant();
        }
    }

    /**
     * Set error state
     */
    private setError(error: string): void {
        this.state.value.error = error;
    }

    /**
     * Clear error state
     */
    public clearError(): void {
        this.state.value.error = null;
    }

    /**
     * Unsubscribe from tenant changes
     */
    private unsubscribeFromTenant(): void {
        if (this.tenantUnsubscribe) {
            this.tenantUnsubscribe();
            this.tenantUnsubscribe = null;
        }
    }

    /**
     * Cleanup resources
     */
    public destroy(): void {
        this.unsubscribeFromTenant();
        this.state.value = {
            isLoading: false,
            error: null,
            userData: null,
            tenant: null,
            tenantId: null,
            serviceConfig: {
                sms: { enabled: false },
                call: { enabled: false },
                ai_call: { enabled: false },
                useSharedNumber: false,
                useTwilioSharedNumber: false
            }
        };
    }
}

/**
 * Composable function to use the number config service
 */
export function useNumberConfigService() {
    const service = new NumberConfigService();

    return {
        // State
        state: service.state,
        isReady: service.isReady,
        hasError: service.hasError,
        tenantId: service.tenantId,
        tenant: service.tenant,
        serviceConfig: service.serviceConfig,

        // Methods
        initialize: () => service.initialize(),
        updateServiceConfig: (config: ServiceConfiguration) => service.updateServiceConfig(config),
        updateTenantWithNumbers: (config: ServiceConfiguration, results: Record<string, NumberPurchaseResponse>) => service.updateTenantWithNumbers(config, results),
        isServiceEnabledInLeadSources: (serviceType: 'sms' | 'call' | 'ai_call') => service.isServiceEnabledInLeadSources(serviceType),
        getValidationStatus: () => service.getValidationStatus(),
        refresh: () => service.refresh(),
        clearError: () => service.clearError(),
        destroy: () => service.destroy()
    };
}
