<template>
    <div class="onboarding-wizard min-h-screen bg-surface-50 dark:bg-surface-900">
        <!-- Header -->
        <div class="bg-white dark:bg-surface-800 border-b border-surface-200 dark:border-surface-700 px-6 py-4">
            <div class="max-w-6xl mx-auto">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-surface-900 dark:text-surface-0">
                            AI Prompt Configuration
                        </h1>
                        <p class="text-surface-600 dark:text-surface-400 mt-1">
                            Configure how your AI assistants interact with customers and analyze leads
                        </p>
                    </div>
                    <div class="flex items-center gap-3">
                        <div v-if="overallSaveStatus" class="flex items-center gap-2 text-sm">
                            <i 
                                :class="[
                                    overallSaveStatus === 'saving' ? 'pi pi-spin pi-spinner text-blue-500' :
                                    overallSaveStatus === 'saved' ? 'pi pi-check text-green-500' :
                                    overallSaveStatus === 'error' ? 'pi pi-exclamation-triangle text-red-500' :
                                    'pi pi-circle text-surface-400'
                                ]"
                            ></i>
                            <span :class="[
                                overallSaveStatus === 'saving' ? 'text-blue-600' :
                                overallSaveStatus === 'saved' ? 'text-green-600' :
                                overallSaveStatus === 'error' ? 'text-red-600' :
                                'text-surface-500'
                            ]">
                                {{ overallSaveStatusText }}
                            </span>
                        </div>
                        <Button
                            label="Save All"
                            icon="pi pi-save"
                            :disabled="!hasChanges || isSaving"
                            :loading="isSaving"
                            @click="saveAll"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="max-w-6xl mx-auto px-6 py-8">
            <!-- Progress Steps -->
            <div class="mb-8">
                <Steps :model="stepItems" :activeStep="currentStep" class="mb-6">
                    <template #item="{ item, active, index }">
                        <div class="flex flex-col items-center gap-2 cursor-pointer" @click="goToStep(index)">
                            <div
                                :class="[
                                    'w-12 h-12 rounded-full flex items-center justify-center text-sm font-medium transition-colors',
                                    active ? 'bg-primary text-white' : 
                                    item.completed ? 'bg-green-500 text-white' : 
                                    'bg-surface-200 dark:bg-surface-700 text-surface-600 dark:text-surface-400 hover:bg-surface-300 dark:hover:bg-surface-600'
                                ]"
                            >
                                <i v-if="item.completed" class="pi pi-check"></i>
                                <i v-else :class="item.icon"></i>
                            </div>
                            <div class="text-center">
                                <div class="font-medium text-sm">{{ item.title }}</div>
                                <div class="text-xs text-surface-500 dark:text-surface-400 max-w-32">
                                    {{ item.description }}
                                </div>
                            </div>
                        </div>
                    </template>
                </Steps>
            </div>

            <!-- Step Content -->
            <div class="bg-white dark:bg-surface-800 rounded-lg shadow-sm border border-surface-200 dark:border-surface-700">
                <!-- Step 1: Chatbot Prompt -->
                <div v-if="currentStep === 0" class="p-6">
                    <ChatbotPromptEditor
                        ref="chatbotEditorRef"
                        v-model="chatbotConfig"
                        :tenant-id="tenantId"
                        :auto-save="autoSaveEnabled"
                        @save="onChatbotSave"
                        @validate="onChatbotValidate"
                    />
                </div>

                <!-- Step 2: Lead Analysis Prompt -->
                <div v-if="currentStep === 1" class="p-6">
                    <LeadAnalysisPromptEditor
                        ref="analysisEditorRef"
                        v-model="analysisConfig"
                        :tenant-id="tenantId"
                        :auto-save="autoSaveEnabled"
                        @save="onAnalysisSave"
                        @validate="onAnalysisValidate"
                    />
                </div>

                <!-- Step 3: Follow-up Prompt -->
                <div v-if="currentStep === 2" class="p-6">
                    <FollowupPromptEditor
                        ref="followupEditorRef"
                        v-model="followupConfig"
                        :tenant-id="tenantId"
                        :auto-save="autoSaveEnabled"
                        @save="onFollowupSave"
                        @validate="onFollowupValidate"
                    />
                </div>
            </div>

            <!-- Navigation -->
            <div class="flex justify-between items-center mt-6">
                <Button
                    label="Previous"
                    icon="pi pi-arrow-left"
                    outlined
                    :disabled="currentStep === 0"
                    @click="previousStep"
                />

                <div class="text-sm text-surface-500 dark:text-surface-400">
                    Step {{ currentStep + 1 }} of {{ stepItems.length }}
                </div>

                <Button
                    v-if="currentStep < stepItems.length - 1"
                    label="Next"
                    icon="pi pi-arrow-right"
                    iconPos="right"
                    :disabled="!isCurrentStepValid"
                    @click="nextStep"
                />
                <Button
                    v-else
                    label="Complete Setup"
                    icon="pi pi-check"
                    iconPos="right"
                    :disabled="!allStepsValid"
                    @click="completeSetup"
                />
            </div>
        </div>

        <!-- Completion Dialog -->
        <Dialog
            v-model:visible="showCompletionDialog"
            modal
            header="Setup Complete!"
            :style="{ width: '450px' }"
            :closable="false"
        >
            <div class="text-center py-4">
                <div class="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="pi pi-check text-green-600 dark:text-green-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0 mb-2">
                    AI Prompts Configured Successfully!
                </h3>
                <p class="text-surface-600 dark:text-surface-400 mb-4">
                    Your AI assistants are now ready to interact with customers, analyze leads, and generate follow-up messages.
                </p>
                <div class="flex justify-center gap-3">
                    <Button
                        label="View Dashboard"
                        icon="pi pi-home"
                        @click="goToDashboard"
                    />
                    <Button
                        label="Test AI Responses"
                        icon="pi pi-play"
                        outlined
                        @click="testAI"
                    />
                </div>
            </div>
        </Dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useToast } from 'primevue/usetoast';
import Steps from 'primevue/steps';
import Button from 'primevue/button';
import Dialog from 'primevue/dialog';

import ChatbotPromptEditor from './components/ChatbotPromptEditor.vue';
import LeadAnalysisPromptEditor from './components/LeadAnalysisPromptEditor.vue';
import FollowupPromptEditor from './components/FollowupPromptEditor.vue';

import { useAuthStore } from '@/entities/auth/store/authStore';
import { useTenantStore } from '@/entities/tenant/store/tenantStore';
import { useReactiveAutoSave } from './services/autoSaveService';

import type { 
    ChatPromptConfig, 
    AnalysisPromptConfig, 
    FollowupPromptConfig,
    OnboardingStep 
} from './types';
import { ONBOARDING_STEPS, DEFAULT_OPENAI_PARAMETERS } from './types';

// Props
interface Props {
    autoSaveEnabled?: boolean;
    completionRedirect?: string;
}

const props = withDefaults(defineProps<Props>(), {
    autoSaveEnabled: true,
    completionRedirect: '/dashboard'
});

// Emits
const emit = defineEmits<{
    'complete': [data: any];
    'step-change': [step: number];
}>();

// Stores and services
const router = useRouter();
const toast = useToast();
const authStore = useAuthStore();
const tenantStore = useTenantStore();

// Get tenant ID
const tenantId = computed(() => authStore.userData?.tenantId || '');

// Auto-save service
const { saveStatus, autoSave, cancelAll } = useReactiveAutoSave(tenantId.value);

// Local state
const currentStep = ref(0);
const showCompletionDialog = ref(false);
const isSaving = ref(false);

// Step validation states
const stepValidation = ref({
    chatbot: false,
    analysis: false,
    followup: false
});

// Configuration states
const chatbotConfig = ref<ChatPromptConfig>({
    prompt: '',
    ...DEFAULT_OPENAI_PARAMETERS
});

const analysisConfig = ref<AnalysisPromptConfig>({
    prompt: '',
    ...DEFAULT_OPENAI_PARAMETERS
});

const followupConfig = ref<FollowupPromptConfig & { prompt?: string }>({
    prompt: '',
    ...DEFAULT_OPENAI_PARAMETERS
});

// Component refs
const chatbotEditorRef = ref();
const analysisEditorRef = ref();
const followupEditorRef = ref();

// Computed
const stepItems = computed<OnboardingStep[]>(() => 
    ONBOARDING_STEPS.map((step, index) => ({
        ...step,
        completed: index < currentStep.value || (index === currentStep.value && isCurrentStepValid.value)
    }))
);

const isCurrentStepValid = computed(() => {
    switch (currentStep.value) {
        case 0: return stepValidation.value.chatbot;
        case 1: return stepValidation.value.analysis;
        case 2: return stepValidation.value.followup;
        default: return false;
    }
});

const allStepsValid = computed(() => 
    stepValidation.value.chatbot && 
    stepValidation.value.analysis && 
    stepValidation.value.followup
);

const hasChanges = computed(() => 
    chatbotConfig.value.prompt.length > 0 ||
    analysisConfig.value.prompt.length > 0 ||
    followupConfig.value.prompt?.length > 0
);

const overallSaveStatus = computed(() => {
    const statuses = Object.values(saveStatus.value);
    if (statuses.includes('saving')) return 'saving';
    if (statuses.includes('error')) return 'error';
    if (statuses.includes('saved')) return 'saved';
    return 'idle';
});

const overallSaveStatusText = computed(() => {
    switch (overallSaveStatus.value) {
        case 'saving': return 'Saving changes...';
        case 'saved': return 'All changes saved';
        case 'error': return 'Save failed';
        default: return '';
    }
});

// Methods
const goToStep = (step: number) => {
    if (step >= 0 && step < stepItems.value.length) {
        currentStep.value = step;
        emit('step-change', step);
    }
};

const nextStep = () => {
    if (currentStep.value < stepItems.value.length - 1 && isCurrentStepValid.value) {
        currentStep.value++;
        emit('step-change', currentStep.value);
    }
};

const previousStep = () => {
    if (currentStep.value > 0) {
        currentStep.value--;
        emit('step-change', currentStep.value);
    }
};

// Save handlers
const onChatbotSave = (config: ChatPromptConfig) => {
    autoSave.chatPrompt(config);
};

const onAnalysisSave = (config: AnalysisPromptConfig) => {
    autoSave.analysisPrompt(config);
};

const onFollowupSave = (config: FollowupPromptConfig & { prompt?: string }) => {
    autoSave.followupPrompt(config);
};

// Validation handlers
const onChatbotValidate = (isValid: boolean) => {
    stepValidation.value.chatbot = isValid;
};

const onAnalysisValidate = (isValid: boolean) => {
    stepValidation.value.analysis = isValid;
};

const onFollowupValidate = (isValid: boolean) => {
    stepValidation.value.followup = isValid;
};

const saveAll = async () => {
    try {
        isSaving.value = true;
        
        const updateData = {
            chat: { prompt: chatbotConfig.value },
            analysis: { prompt: analysisConfig.value },
            followup: followupConfig.value
        };

        await tenantStore.updateTenant(tenantId.value, updateData);
        
        toast.add({
            severity: 'success',
            summary: 'Saved',
            detail: 'All prompt configurations saved successfully',
            life: 3000
        });
    } catch (error) {
        console.error('Save failed:', error);
        toast.add({
            severity: 'error',
            summary: 'Save Failed',
            detail: 'Failed to save configurations. Please try again.',
            life: 5000
        });
    } finally {
        isSaving.value = false;
    }
};

const completeSetup = async () => {
    if (!allStepsValid.value) return;
    
    await saveAll();
    showCompletionDialog.value = true;
    
    emit('complete', {
        chatbot: chatbotConfig.value,
        analysis: analysisConfig.value,
        followup: followupConfig.value
    });
};

const goToDashboard = () => {
    router.push(props.completionRedirect);
};

const testAI = () => {
    // Navigate to AI testing page or open test dialog
    router.push('/leads'); // or wherever AI testing is available
};

// Load existing configurations
const loadExistingConfigs = async () => {
    try {
        const tenant = await tenantStore.getTenant(tenantId.value);
        if (tenant) {
            if (tenant.chat?.prompt) {
                chatbotConfig.value = { ...DEFAULT_OPENAI_PARAMETERS, ...tenant.chat.prompt };
            }
            if (tenant.analysis?.prompt) {
                analysisConfig.value = { ...DEFAULT_OPENAI_PARAMETERS, ...tenant.analysis.prompt };
            }
            if (tenant.followup) {
                followupConfig.value = { ...DEFAULT_OPENAI_PARAMETERS, ...tenant.followup };
            }
        }
    } catch (error) {
        console.error('Failed to load existing configurations:', error);
    }
};

// Lifecycle
onMounted(() => {
    if (tenantId.value) {
        loadExistingConfigs();
    }
});

onUnmounted(() => {
    cancelAll();
});
</script>

<style scoped>
.onboarding-wizard {
    min-height: 100vh;
}

:deep(.p-steps .p-steps-item) {
    flex: 1;
}

:deep(.p-dialog-content) {
    padding: 0;
}
</style>
