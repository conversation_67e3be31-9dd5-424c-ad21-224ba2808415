# AI Prompt Onboarding Feature

This feature provides a comprehensive onboarding wizard for configuring AI prompts used throughout the CRM system. It allows users to set up and customize three types of AI prompts: chatbot interactions, lead analysis, and follow-up message generation.

## Features

- **Multi-step Wizard**: Guided setup process with clear navigation and progress tracking
- **Auto-save Functionality**: Automatic saving of changes as users type with visual feedback
- **OpenAI Parameter Configuration**: User-friendly sliders for fine-tuning AI behavior
- **Comprehensive Help Content**: Detailed explanations and examples for each prompt type
- **Responsive Design**: Built with PrimeVue components for excellent UX across devices
- **Real-time Validation**: Immediate feedback on prompt content and configuration

## Prompt Types

### 1. Chatbot Prompt
- **Purpose**: Defines how AI responds to customer inquiries across all channels
- **Usage**: Email, SMS, WhatsApp, social media interactions
- **Configuration**: Personality, knowledge base, response style, brand voice

### 2. Lead Analysis Prompt
- **Purpose**: Instructs AI on how to analyze and qualify incoming leads
- **Usage**: Automatic lead scoring, data extraction, intent detection
- **Configuration**: Qualification criteria, data extraction rules, scoring logic

### 3. Follow-up Prompt
- **Purpose**: Generates personalized follow-up messages for different channels
- **Usage**: Automated nurturing sequences, appointment reminders, re-engagement
- **Configuration**: Channel-specific tone, personalization rules, CTA strategies

## OpenAI Parameters

Each prompt type includes configurable OpenAI parameters:

- **Temperature (Creativity Level)**: Controls randomness and creativity (0-2)
- **Frequency Penalty (Repetition Reduction)**: Reduces word/phrase repetition (-2 to 2)
- **Presence Penalty (Topic Diversity)**: Encourages new topic introduction (-2 to 2)
- **Top P (Focus Control)**: Controls response diversity via nucleus sampling (0-1)

## Components

### OnboardingWizard
Main orchestrator component with step navigation and progress tracking.

```vue
<template>
  <OnboardingWizard
    :auto-save-enabled="true"
    completion-redirect="/dashboard"
    @complete="onSetupComplete"
    @step-change="onStepChange"
  />
</template>
```

### Individual Prompt Editors
Each prompt type has its own specialized editor component:

```vue
<template>
  <!-- Chatbot Prompt Editor -->
  <ChatbotPromptEditor
    v-model="chatbotConfig"
    :tenant-id="tenantId"
    :auto-save="true"
    @save="onSave"
    @validate="onValidate"
  />

  <!-- Lead Analysis Prompt Editor -->
  <LeadAnalysisPromptEditor
    v-model="analysisConfig"
    :tenant-id="tenantId"
    :auto-save="true"
    @save="onSave"
    @validate="onValidate"
  />

  <!-- Follow-up Prompt Editor -->
  <FollowupPromptEditor
    v-model="followupConfig"
    :tenant-id="tenantId"
    :auto-save="true"
    @save="onSave"
    @validate="onValidate"
  />
</template>
```

### OpenAI Parameters Component
Reusable component for configuring AI behavior parameters:

```vue
<template>
  <OpenAIParameters
    v-model="parameters"
    :show-presets="true"
    :show-reset="true"
    @change="onParametersChange"
  />
</template>
```

## Services

### Auto-save Service
Handles automatic saving with debouncing and retry logic:

```typescript
import { useReactiveAutoSave } from '@/features/onboarding';

const { saveStatus, autoSave } = useReactiveAutoSave(tenantId);

// Auto-save configurations
autoSave.chatPrompt(chatbotConfig);
autoSave.analysisPrompt(analysisConfig);
autoSave.followupPrompt(followupConfig);
```

## Data Structure

The feature extends the Tenant interface with prompt configurations:

```typescript
interface Tenant {
  // ... existing properties
  
  // AI Prompt Configurations
  chat?: {
    prompt?: {
      prompt: string;
      temperature: number;
      frequency_penalty: number;
      presence_penalty: number;
      top_p: number;
    };
  };
  
  analysis?: {
    prompt?: {
      prompt: string;
      temperature: number;
      frequency_penalty: number;
      presence_penalty: number;
      top_p: number;
    };
  };
  
  followup?: {
    prompt?: string;
    temperature: number;
    frequency_penalty: number;
    presence_penalty: number;
    top_p: number;
  };
}
```

## Usage

### 1. Add to Router
```typescript
{
  path: '/onboarding/prompts',
  name: 'PromptOnboarding',
  component: () => import('@/features/onboarding/OnboardingWizard.vue'),
  meta: { requiresAuth: true }
}
```

### 2. Use as Component
```vue
<script setup>
import { OnboardingWizard } from '@/features/onboarding';

const onSetupComplete = (data) => {
  console.log('Prompt setup completed:', data);
  // Redirect or show success message
};
</script>

<template>
  <OnboardingWizard @complete="onSetupComplete" />
</template>
```

## Styling

The feature uses PrimeVue's design system with custom styling for:
- Step navigation with hover effects
- Parameter sliders with branded colors
- Auto-save status indicators
- Responsive grid layouts
- Dark mode support

## Dependencies

- **PrimeVue**: UI components (Steps, Button, Card, Slider, Textarea, Dialog)
- **Vue 3**: Composition API, reactivity system
- **Pinia**: State management (tenant store)
- **Firebase**: Real-time data synchronization
- **TypeScript**: Type safety and better DX

## Error Handling

- **Auto-save Failures**: Retry mechanism with exponential backoff
- **Validation Errors**: Real-time feedback with clear error messages
- **Network Issues**: Graceful degradation with offline support
- **Data Conflicts**: Conflict resolution with user notification

## Testing

The feature includes comprehensive validation and error handling:
- Prompt content validation (minimum length, required fields)
- Parameter range validation
- Auto-save status tracking
- Network error recovery
- User input sanitization
