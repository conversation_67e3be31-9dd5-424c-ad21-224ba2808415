/**
 * Email numbers-onboarding feature types and interfaces
 */

/**
 * Email configuration interface for tenant
 */
export interface EmailConfig {
    defaultEmail?: string;
    emails?: string[];
    validated?: boolean;
    template?: string;
}

/**
 * Onboarding step interface
 */
export interface OnboardingStep {
    id: number;
    title: string;
    description: string;
    icon: string;
    completed: boolean;
}

/**
 * Email validation form data
 */
export interface EmailValidationForm {
    email: string;
    isValid: boolean;
    error?: string;
}

/**
 * Email forwarding instructions for different providers
 */
export interface EmailProvider {
    name: string;
    icon: string;
    instructions: string[];
    helpUrl?: string;
}

/**
 * Template validation result
 */
export interface TemplateValidation {
    isValid: boolean;
    hasContentPlaceholder: boolean;
    errors: string[];
}

/**
 * Onboarding state interface
 */
export interface OnboardingState {
    currentStep: number;
    steps: OnboardingStep[];
    emailForm: EmailValidationForm;
    isValidating: boolean;
    isWaitingForValidation: boolean;
    template: string;
    templateValidation: TemplateValidation;
}

/**
 * Email providers configuration
 */
export const EMAIL_PROVIDERS: EmailProvider[] = [
    {
        name: 'Gmail',
        icon: 'pi pi-google',
        instructions: [
            'Open Gmail and click the gear icon in the top right',
            'Select "See all settings"',
            'Go to the "Forwarding and POP/IMAP" tab',
            'Click "Add a forwarding address"',
            'Enter the forwarding email address provided',
            'Click "Next" and then "Proceed"',
            'Check your email for a verification message',
            'Click the verification link in the email',
            'Return to Gmail settings and select "Forward a copy of incoming mail"',
            'Choose the forwarding address and click "Save Changes"'
        ],
        helpUrl: 'https://support.google.com/mail/answer/10957?hl=en'
    },
    {
        name: 'Outlook',
        icon: 'pi pi-microsoft',
        instructions: [
            'Sign in to Outlook.com',
            'Go to Settings (gear icon) > View all Outlook settings',
            'Select "Mail" > "Forwarding"',
            'Check "Enable forwarding"',
            'Enter the forwarding email address provided',
            'Choose whether to keep a copy in your inbox',
            'Click "Save"'
        ],
        helpUrl: 'https://support.microsoft.com/en-us/office/turn-on-automatic-forwarding-in-outlook-com-7f2670a1-7fff-4475-8a3c-5822d63b0c8e'
    },
    {
        name: 'Yahoo Mail',
        icon: 'pi pi-yahoo',
        instructions: [
            'Sign in to Yahoo Mail',
            'Click the Settings icon (gear) in the upper right',
            'Click "More Settings"',
            'Click "Mailboxes" in the left panel',
            'Select your email address',
            'Click "Server Settings"',
            'In the "Forwarding" section, enter the forwarding address',
            'Click "Save"'
        ],
        helpUrl: 'https://help.yahoo.com/kb/SLN22026.html'
    },
    {
        name: 'Apple iCloud',
        icon: 'pi pi-apple',
        instructions: ['Sign in to iCloud.com', 'Click Mail', 'Click the gear icon and choose "Preferences"', 'Click the "General" tab', 'In the "Forwarding" section, enter the forwarding address', 'Click "Done"'],
        helpUrl: 'https://support.apple.com/en-us/HT204570'
    }
];

/**
 * Default email template
 */
export const DEFAULT_EMAIL_TEMPLATE = `Hello,

Thank you for your inquiry. Our team has reviewed your message and here's our response:

{{content}}

If you have any further questions, please don't hesitate to reach out.

Best regards,
The Team`;
